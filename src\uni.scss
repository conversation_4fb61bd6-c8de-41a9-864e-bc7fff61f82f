/**
 * 响应式设计系统 - 基于iPhone XS MAX (390px) 设计稿
 * 使用pxToRpx转换函数实现完美适配
 * 设计稿基准: 390px -> 750rpx
 */

/* ================== 适配转换函数 ================== */
/*
 * 转换公式: rpx = (px / 390) * 750
 * 例如: 100px = (100 / 390) * 750 = 192.31rpx
 */

/* ================== 主题色彩系统 ================== */
:root {
  /* 主色调 - 清新蓝绿色系 */
  --primary-color: #00b4d8;
  --primary-light: #90e0ef;
  --primary-dark: #0077b6;
  --primary-gradient: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);

  /* 辅助色系 */
  --secondary-color: #8b5cf6;
  --accent-color: #06ffa5;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --success-color: #10b981;

  /* 中性色系 - 优雅灰色调 */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* 背景色系 */
  --bg-primary: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  --bg-secondary: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  --bg-card: rgba(255, 255, 255, 0.9);
  --bg-card-hover: rgba(255, 255, 255, 0.95);
  --bg-glass: rgba(255, 255, 255, 0.85);

  /* 文字颜色 */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #94a3b8;
  --text-inverse: #ffffff;

  /* ================== 响应式尺寸系统 ================== */
  /* 基于390px设计稿的精确转换 */

  /* 字体大小 - 基于设计稿精确转换 */
  --font-xs: 19.23rpx;    /* 10px -> (10/390)*750 */
  --font-sm: 23.08rpx;    /* 12px -> (12/390)*750 */
  --font-base: 26.92rpx;  /* 14px -> (14/390)*750 */
  --font-lg: 30.77rpx;    /* 16px -> (16/390)*750 */
  --font-xl: 34.62rpx;    /* 18px -> (18/390)*750 */
  --font-2xl: 42.31rpx;   /* 22px -> (22/390)*750 */
  --font-3xl: 50rpx;      /* 26px -> (26/390)*750 */
  --font-4xl: 61.54rpx;   /* 32px -> (32/390)*750 */

  /* 圆角系统 - 基于设计稿精确转换 */
  --radius-xs: 3.85rpx;   /* 2px -> (2/390)*750 */
  --radius-sm: 7.69rpx;   /* 4px -> (4/390)*750 */
  --radius-base: 11.54rpx; /* 6px -> (6/390)*750 */
  --radius-lg: 15.38rpx;  /* 8px -> (8/390)*750 */
  --radius-xl: 23.08rpx;  /* 12px -> (12/390)*750 */
  --radius-2xl: 30.77rpx; /* 16px -> (16/390)*750 */
  --radius-3xl: 38.46rpx; /* 20px -> (20/390)*750 */
  --radius-full: 9999rpx;

  /* 间距系统 - 基于设计稿精确转换 */
  --space-xs: 7.69rpx;    /* 4px -> (4/390)*750 */
  --space-sm: 15.38rpx;   /* 8px -> (8/390)*750 */
  --space-base: 23.08rpx; /* 12px -> (12/390)*750 */
  --space-lg: 30.77rpx;   /* 16px -> (16/390)*750 */
  --space-xl: 38.46rpx;   /* 20px -> (20/390)*750 */
  --space-2xl: 46.15rpx;  /* 24px -> (24/390)*750 */
  --space-3xl: 61.54rpx;  /* 32px -> (32/390)*750 */
  --space-4xl: 76.92rpx;  /* 40px -> (40/390)*750 */

  /* 阴影系统 - 基于设计稿精确转换 */
  --shadow-sm: 0 3.85rpx 7.69rpx rgba(0, 0, 0, 0.06);    /* 0 2px 4px */
  --shadow-base: 0 7.69rpx 11.54rpx rgba(0, 0, 0, 0.07); /* 0 4px 6px */
  --shadow-lg: 0 19.23rpx 28.85rpx rgba(0, 0, 0, 0.1);   /* 0 10px 15px */
  --shadow-xl: 0 38.46rpx 48.08rpx rgba(0, 0, 0, 0.1);   /* 0 20px 25px */
  --shadow-2xl: 0 48.08rpx 96.15rpx rgba(0, 0, 0, 0.25); /* 0 25px 50px */

  /* 组件尺寸 - 基于设计稿精确转换 */
  --button-height: 84.62rpx;    /* 44px -> (44/390)*750 */
  --input-height: 76.92rpx;     /* 40px -> (40/390)*750 */
  --card-padding: 30.77rpx;     /* 16px -> (16/390)*750 */
  --section-spacing: 46.15rpx;  /* 24px -> (24/390)*750 */

  /* 图标尺寸 - 基于设计稿精确转换 */
  --icon-xs: 23.08rpx;    /* 12px -> (12/390)*750 */
  --icon-sm: 30.77rpx;    /* 16px -> (16/390)*750 */
  --icon-base: 38.46rpx;  /* 20px -> (20/390)*750 */
  --icon-lg: 46.15rpx;    /* 24px -> (24/390)*750 */
  --icon-xl: 61.54rpx;    /* 32px -> (32/390)*750 */
  --icon-2xl: 76.92rpx;   /* 40px -> (40/390)*750 */

  /* ================== 状态色系 ================== */

  /* 辐射监测状态色 */
  --radiation-safe: #10b981;
  --radiation-safe-bg: rgba(16, 185, 129, 0.1);
  --radiation-safe-border: rgba(16, 185, 129, 0.2);

  --radiation-warning: #f59e0b;
  --radiation-warning-bg: rgba(245, 158, 11, 0.1);
  --radiation-warning-border: rgba(245, 158, 11, 0.2);

  --radiation-danger: #ef4444;
  --radiation-danger-bg: rgba(239, 68, 68, 0.1);
  --radiation-danger-border: rgba(239, 68, 68, 0.2);

  /* ================== 组件变量 ================== */

  /* 卡片 */
  --card-bg: var(--bg-card);
  --card-border: rgba(226, 232, 240, 0.8);
  --card-shadow: var(--shadow-lg);
  --card-radius: var(--radius-xl);

  /* 按钮 */
  --btn-radius: var(--radius-lg);
  --btn-shadow: var(--shadow-base);

  /* 输入框 */
  --input-bg: var(--white);
  --input-border: var(--gray-300);
  --input-border-focus: var(--primary-color);
  --input-radius: var(--radius-base);

  /* ================== 动画变量 ================== */
  --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  /* ================== 兼容性变量 ================== */

  /* 兼容原有uni-app变量 */
  --uni-color-primary: var(--primary-color);
  --uni-color-success: var(--success-color);
  --uni-color-warning: var(--warning-color);
  --uni-color-error: var(--danger-color);

  --uni-text-color: var(--text-primary);
  --uni-text-color-inverse: var(--text-inverse);
  --uni-text-color-grey: var(--text-muted);
  --uni-text-color-placeholder: var(--gray-400);
  --uni-text-color-disable: var(--gray-300);

  --uni-bg-color: var(--white);
  --uni-bg-color-grey: var(--gray-100);
  --uni-bg-color-hover: var(--gray-50);
  --uni-bg-color-mask: rgba(15, 23, 42, 0.5);

  --uni-border-color: var(--gray-300);

  --uni-font-size-sm: var(--font-sm);
  --uni-font-size-base: var(--font-base);
  --uni-font-size-lg: var(--font-lg);

  --uni-border-radius-sm: var(--radius-sm);
  --uni-border-radius-base: var(--radius-base);
  --uni-border-radius-lg: var(--radius-lg);
  --uni-border-radius-circle: 50%;

  --uni-spacing-row-sm: var(--space-xs);
  --uni-spacing-row-base: var(--space-base);
  --uni-spacing-row-lg: var(--space-lg);

  --uni-spacing-col-sm: var(--space-xs);
  --uni-spacing-col-base: var(--space-base);
  --uni-spacing-col-lg: var(--space-lg);

  --uni-opacity-disabled: 0.5;

  /* 文章场景相关 */
  --uni-color-title: var(--text-primary);
  --uni-font-size-title: var(--font-xl);
  --uni-color-subtitle: var(--text-secondary);
  --uni-font-size-subtitle: var(--font-lg);
  --uni-color-paragraph: var(--text-secondary);
  --uni-font-size-paragraph: var(--font-base);

  /* ================== 响应式断点 ================== */
  /* 基于常见设备宽度设置断点 */
  --breakpoint-xs: 320px;   /* 小屏手机 */
  --breakpoint-sm: 375px;   /* 中等手机 */
  --breakpoint-md: 390px;   /* 设计基准 iPhone XS MAX */
  --breakpoint-lg: 414px;   /* 大屏手机 */
  --breakpoint-xl: 430px;   /* 目标屏幕尺寸 */
  --breakpoint-xxl: 768px;  /* 平板 */
}

/* ================== 响应式媒体查询 ================== */

/* 超小屏设备 (width < 320px) */
@media (max-width: 320px) {
  :root {
    /* 字体缩小 */
    --font-xs: 17.31rpx;    /* 9px */
    --font-sm: 21.15rpx;    /* 11px */
    --font-base: 25rpx;     /* 13px */
    --font-lg: 28.85rpx;    /* 15px */
    --font-xl: 32.69rpx;    /* 17px */
    --font-2xl: 40.38rpx;   /* 21px */
    --font-3xl: 48.08rpx;   /* 25px */
    --font-4xl: 59.62rpx;   /* 31px */

    /* 间距缩小 */
    --space-xs: 5.77rpx;    /* 3px */
    --space-sm: 11.54rpx;   /* 6px */
    --space-base: 19.23rpx; /* 10px */
    --space-lg: 26.92rpx;   /* 14px */
    --space-xl: 34.62rpx;   /* 18px */
    --space-2xl: 42.31rpx;  /* 22px */
    --space-3xl: 57.69rpx;  /* 30px */
    --space-4xl: 73.08rpx;  /* 38px */
  }
}

/* 小屏设备 (320px <= width < 375px) */
@media (min-width: 320px) and (max-width: 374px) {
  :root {
    /* 字体略微缩小 */
    --font-xs: 18.27rpx;    /* 9.5px */
    --font-sm: 22.12rpx;    /* 11.5px */
    --font-base: 25.96rpx;  /* 13.5px */
    --font-lg: 29.81rpx;    /* 15.5px */
    --font-xl: 33.65rpx;    /* 17.5px */
    --font-2xl: 41.35rpx;   /* 21.5px */
    --font-3xl: 49.04rpx;   /* 25.5px */
    --font-4xl: 60.58rpx;   /* 31.5px */

    /* 间距略微缩小 */
    --space-xs: 6.73rpx;    /* 3.5px */
    --space-sm: 13.46rpx;   /* 7px */
    --space-base: 21.15rpx; /* 11px */
    --space-lg: 28.85rpx;   /* 15px */
    --space-xl: 36.54rpx;   /* 19px */
    --space-2xl: 44.23rpx;  /* 23px */
    --space-3xl: 59.62rpx;  /* 31px */
    --space-4xl: 75rpx;     /* 39px */
  }
}

/* 中等屏设备 (375px <= width < 390px) - 接近设计基准 */
@media (min-width: 375px) and (max-width: 389px) {
  :root {
    /* 使用接近基准的尺寸 */
    --font-xs: 18.75rpx;    /* 9.75px */
    --font-sm: 22.5rpx;     /* 11.75px */
    --font-base: 26.25rpx;  /* 13.75px */
    --font-lg: 30rpx;       /* 15.75px */
    --font-xl: 33.75rpx;    /* 17.75px */
    --font-2xl: 41.25rpx;   /* 21.75px */
    --font-3xl: 48.75rpx;   /* 25.75px */
    --font-4xl: 60rpx;      /* 31.75px */

    /* 间距接近基准 */
    --space-xs: 7.5rpx;     /* 3.75px */
    --space-sm: 15rpx;      /* 7.75px */
    --space-base: 22.5rpx;  /* 11.75px */
    --space-lg: 30rpx;      /* 15.75px */
    --space-xl: 37.5rpx;    /* 19.75px */
    --space-2xl: 45rpx;     /* 23.75px */
    --space-3xl: 60rpx;     /* 31.75px */
    --space-4xl: 75rpx;     /* 39.75px */
  }
}

/* 设计基准 (390px) - 保持原始尺寸 */
@media (min-width: 390px) and (max-width: 389px) {
  /* 使用默认的CSS变量值 */
}

/* 目标屏幕尺寸 (430px) - 完美适配设计图 */
@media (min-width: 430px) and (max-width: 430px) {
  :root {
    /* 字体按照430/390的比例精确缩放 */
    --font-xs: 21.21rpx;    /* 10px * 1.1026 */
    --font-sm: 25.45rpx;    /* 12px * 1.1026 */
    --font-base: 29.69rpx;  /* 14px * 1.1026 */
    --font-lg: 33.93rpx;    /* 16px * 1.1026 */
    --font-xl: 38.18rpx;    /* 18px * 1.1026 */
    --font-2xl: 46.67rpx;   /* 22px * 1.1026 */
    --font-3xl: 55.13rpx;   /* 26px * 1.1026 */
    --font-4xl: 67.85rpx;   /* 32px * 1.1026 */

    /* 间距按照430/390的比例精确缩放 */
    --space-xs: 8.48rpx;    /* 4px * 1.1026 */
    --space-sm: 16.97rpx;   /* 8px * 1.1026 */
    --space-base: 25.45rpx; /* 12px * 1.1026 */
    --space-lg: 33.93rpx;   /* 16px * 1.1026 */
    --space-xl: 42.42rpx;   /* 20px * 1.1026 */
    --space-2xl: 50.90rpx;  /* 24px * 1.1026 */
    --space-3xl: 67.85rpx;  /* 32px * 1.1026 */
    --space-4xl: 84.82rpx;  /* 40px * 1.1026 */

    /* 圆角按比例缩放 */
    --radius-xs: 4.24rpx;   /* 2px * 1.1026 */
    --radius-sm: 8.48rpx;   /* 4px * 1.1026 */
    --radius-base: 12.73rpx; /* 6px * 1.1026 */
    --radius-lg: 16.97rpx;  /* 8px * 1.1026 */
    --radius-xl: 25.45rpx;  /* 12px * 1.1026 */
    --radius-2xl: 33.93rpx; /* 16px * 1.1026 */
    --radius-3xl: 42.42rpx; /* 20px * 1.1026 */

    /* 组件尺寸按比例缩放 */
    --button-height: 93.33rpx;    /* 44px * 1.1026 */
    --input-height: 84.82rpx;     /* 40px * 1.1026 */
    --card-padding: 33.93rpx;     /* 16px * 1.1026 */
    --section-spacing: 50.90rpx;  /* 24px * 1.1026 */

    /* 图标尺寸按比例缩放 */
    --icon-xs: 25.45rpx;    /* 12px * 1.1026 */
    --icon-sm: 33.93rpx;    /* 16px * 1.1026 */
    --icon-base: 42.42rpx;  /* 20px * 1.1026 */
    --icon-lg: 50.90rpx;    /* 24px * 1.1026 */
    --icon-xl: 67.85rpx;    /* 32px * 1.1026 */
    --icon-2xl: 84.82rpx;   /* 40px * 1.1026 */
  }
}

/* 大屏设备 (414px <= width < 768px) */
@media (min-width: 414px) and (max-width: 767px) {
  :root {
    /* 字体放大 */
    --font-xs: 20.19rpx;    /* 10.5px */
    --font-sm: 24.04rpx;    /* 12.5px */
    --font-base: 27.88rpx;  /* 14.5px */
    --font-lg: 31.73rpx;    /* 16.5px */
    --font-xl: 35.58rpx;    /* 18.5px */
    --font-2xl: 43.27rpx;   /* 22.5px */
    --font-3xl: 50.96rpx;   /* 26.5px */
    --font-4xl: 62.5rpx;    /* 32.5px */

    /* 间距放大 */
    --space-xs: 8.65rpx;    /* 4.5px */
    --space-sm: 17.31rpx;   /* 9px */
    --space-base: 25.96rpx; /* 13.5px */
    --space-lg: 34.62rpx;   /* 18px */
    --space-xl: 43.27rpx;   /* 22.5px */
    --space-2xl: 51.92rpx;  /* 27px */
    --space-3xl: 69.23rpx;  /* 36px */
    --space-4xl: 86.54rpx;  /* 45px */
  }
}

/* 平板设备 (width >= 768px) */
@media (min-width: 768px) {
  :root {
    /* 字体进一步放大 */
    --font-xs: 21.15rpx;    /* 11px */
    --font-sm: 25rpx;       /* 13px */
    --font-base: 28.85rpx;  /* 15px */
    --font-lg: 32.69rpx;    /* 17px */
    --font-xl: 36.54rpx;    /* 19px */
    --font-2xl: 44.23rpx;   /* 23px */
    --font-3xl: 51.92rpx;   /* 27px */
    --font-4xl: 63.46rpx;   /* 33px */

    /* 间距进一步放大 */
    --space-xs: 9.62rpx;    /* 5px */
    --space-sm: 19.23rpx;   /* 10px */
    --space-base: 28.85rpx; /* 15px */
    --space-lg: 38.46rpx;   /* 20px */
    --space-xl: 48.08rpx;   /* 25px */
    --space-2xl: 57.69rpx;  /* 30px */
    --space-3xl: 76.92rpx;  /* 40px */
    --space-4xl: 96.15rpx;  /* 50px */
  }
}