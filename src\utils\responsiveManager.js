/**
 * 响应式样式管理器
 * 统一管理应用的响应式样式和适配逻辑
 */

import { getDeviceInfo, pxToRpx, CommonSizes } from './adaptiveUtils.js';

class ResponsiveManager {
  constructor() {
    this.deviceInfo = null;
    this.isInitialized = false;
    this.listeners = [];
    this.currentBreakpoint = 'md'; // 默认中等屏幕
  }

  /**
   * 初始化响应式管理器
   */
  async init() {
    if (this.isInitialized) return;
    
    try {
      this.deviceInfo = await getDeviceInfo();
      this.currentBreakpoint = this.getBreakpoint();
      this.isInitialized = true;
      
      // 通知所有监听器
      this.notifyListeners();
      
      console.log('ResponsiveManager initialized:', {
        deviceInfo: this.deviceInfo,
        breakpoint: this.currentBreakpoint
      });
    } catch (error) {
      console.error('Failed to initialize ResponsiveManager:', error);
    }
  }

  /**
   * 获取当前设备的断点
   * 针对430px屏幕进行专门优化
   */
  getBreakpoint() {
    if (!this.deviceInfo) return 'md';

    const width = this.deviceInfo.windowWidth;

    if (width < 320) return 'xs';      // 超小屏
    if (width < 375) return 'sm';      // 小屏
    if (width < 390) return 'md';      // 中屏 (设计基准)
    if (width < 414) return 'lg';      // 大屏
    if (width < 430) return 'xl';      // 超大屏
    if (width === 430) return 'target'; // 目标屏幕尺寸 (430px)
    if (width < 768) return 'xxl';     // 平板小
    return 'xxxl';  // 平板及以上
  }

  /**
   * 添加响应式变化监听器
   */
  addListener(callback) {
    this.listeners.push(callback);
    
    // 如果已经初始化，立即调用回调
    if (this.isInitialized) {
      callback(this.deviceInfo, this.currentBreakpoint);
    }
  }

  /**
   * 移除监听器
   */
  removeListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.deviceInfo, this.currentBreakpoint);
      } catch (error) {
        console.error('Error in responsive listener:', error);
      }
    });
  }

  /**
   * 获取响应式样式
   */
  getResponsiveStyles(baseStyles) {
    if (!this.deviceInfo) return baseStyles;
    
    const styles = { ...baseStyles };
    const breakpoint = this.currentBreakpoint;
    
    // 根据断点调整样式
    switch (breakpoint) {
      case 'xs':
        return this.adjustStylesForXS(styles);
      case 'sm':
        return this.adjustStylesForSM(styles);
      case 'md':
        return styles; // 基准尺寸 (390px)
      case 'lg':
        return this.adjustStylesForLG(styles);
      case 'xl':
        return this.adjustStylesForXL(styles);
      case 'target':
        return this.adjustStylesForTarget(styles); // 430px 目标尺寸
      case 'xxl':
        return this.adjustStylesForXXL(styles); // 768px
      case 'xxxl':
        return this.adjustStylesForXXXL(styles); // 1024px+
      default:
        return styles;
    }
  }

  /**
   * 超小屏样式调整
   */
  adjustStylesForXS(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 0.85),
      padding: this.scaleValue(styles.padding, 0.8),
      margin: this.scaleValue(styles.margin, 0.8),
      borderRadius: this.scaleValue(styles.borderRadius, 0.9)
    };
  }

  /**
   * 小屏样式调整
   */
  adjustStylesForSM(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 0.9),
      padding: this.scaleValue(styles.padding, 0.9),
      margin: this.scaleValue(styles.margin, 0.9),
      borderRadius: this.scaleValue(styles.borderRadius, 0.95)
    };
  }

  /**
   * 大屏样式调整
   */
  adjustStylesForLG(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 1.1),
      padding: this.scaleValue(styles.padding, 1.1),
      margin: this.scaleValue(styles.margin, 1.1),
      borderRadius: this.scaleValue(styles.borderRadius, 1.05)
    };
  }

  /**
   * 超大屏样式调整
   */
  adjustStylesForXL(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 1.05),
      padding: this.scaleValue(styles.padding, 1.05),
      margin: this.scaleValue(styles.margin, 1.05),
      borderRadius: this.scaleValue(styles.borderRadius, 1.02)
    };
  }

  /**
   * 目标屏幕样式调整 (430px) - 与设计图完全一致
   */
  adjustStylesForTarget(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 1.1026), // 430/390 = 1.1026
      padding: this.scaleValue(styles.padding, 1.1026),
      margin: this.scaleValue(styles.margin, 1.1026),
      borderRadius: this.scaleValue(styles.borderRadius, 1.05),
      // 确保与设计图完全一致的缩放比例
      width: this.scaleValue(styles.width, 1.1026),
      height: this.scaleValue(styles.height, 1.1026)
    };
  }

  /**
   * 平板样式调整 (768px)
   */
  adjustStylesForXXL(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 1.15),
      padding: this.scaleValue(styles.padding, 1.15),
      margin: this.scaleValue(styles.margin, 1.15),
      borderRadius: this.scaleValue(styles.borderRadius, 1.1)
    };
  }

  /**
   * 大平板样式调整 (1024px+)
   */
  adjustStylesForXXXL(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 1.2),
      padding: this.scaleValue(styles.padding, 1.2),
      margin: this.scaleValue(styles.margin, 1.2),
      borderRadius: this.scaleValue(styles.borderRadius, 1.15)
    };
  }

  /**
   * 缩放数值
   */
  scaleValue(value, scale) {
    if (typeof value === 'number') {
      return Math.round(value * scale);
    }
    if (typeof value === 'string' && value.includes('rpx')) {
      const num = parseFloat(value);
      return `${Math.round(num * scale)}rpx`;
    }
    return value;
  }

  /**
   * 获取响应式字体大小
   */
  getResponsiveFontSize(baseFontSize) {
    const breakpoint = this.currentBreakpoint;
    const scales = {
      xs: 0.85,
      sm: 0.9,
      md: 1,        // 390px 基准
      lg: 1.05,     // 414px
      xl: 1.08,     // 接近430px
      target: 1.1026, // 430px - 设计图目标尺寸 (430/390)
      xxl: 1.15,    // 768px
      xxxl: 1.2     // 1024px+
    };

    const scale = scales[breakpoint] || 1;
    return Math.round(baseFontSize * scale);
  }

  /**
   * 获取响应式间距
   */
  getResponsiveSpacing(baseSpacing) {
    const breakpoint = this.currentBreakpoint;
    const scales = {
      xs: 0.8,
      sm: 0.9,
      md: 1,        // 390px 基准
      lg: 1.05,     // 414px
      xl: 1.08,     // 接近430px
      target: 1.1026, // 430px - 设计图目标尺寸 (430/390)
      xxl: 1.15,    // 768px
      xxxl: 1.2     // 1024px+
    };

    const scale = scales[breakpoint] || 1;
    return Math.round(baseSpacing * scale);
  }

  /**
   * 获取当前设备信息
   */
  getDeviceInfo() {
    return this.deviceInfo;
  }

  /**
   * 获取当前断点
   */
  getCurrentBreakpoint() {
    return this.currentBreakpoint;
  }

  /**
   * 判断是否为小屏设备
   */
  isSmallScreen() {
    return ['xs', 'sm'].includes(this.currentBreakpoint);
  }

  /**
   * 判断是否为大屏设备
   */
  isLargeScreen() {
    return ['xl', 'target', 'xxl', 'xxxl'].includes(this.currentBreakpoint);
  }

  /**
   * 判断是否为目标屏幕尺寸 (430px)
   */
  isTargetScreen() {
    return this.currentBreakpoint === 'target';
  }

  /**
   * 获取网格列数
   */
  getGridColumns(defaultColumns = 2) {
    const breakpoint = this.currentBreakpoint;
    const columnMap = {
      xs: Math.max(1, defaultColumns - 1),
      sm: Math.max(1, defaultColumns - 1),
      md: defaultColumns,      // 390px: 保持默认
      lg: defaultColumns,      // 414px: 保持默认
      xl: defaultColumns,      // 接近430px: 保持默认
      target: defaultColumns,  // 430px: 保持默认 (设计图为2列)
      xxl: defaultColumns + 1, // 768px: 增加1列
      xxxl: defaultColumns + 2 // 1024px+: 增加2列
    };

    return columnMap[breakpoint] || defaultColumns;
  }

  /**
   * 创建响应式CSS类名
   */
  createResponsiveClass(baseClass) {
    return `${baseClass} ${baseClass}--${this.currentBreakpoint}`;
  }
}

// 创建全局实例
const responsiveManager = new ResponsiveManager();

// 导出实例和工具函数
export default responsiveManager;

export {
  ResponsiveManager,
  responsiveManager,
  pxToRpx,
  CommonSizes
};

// 自动初始化（在应用启动时）
export const initResponsiveManager = async () => {
  await responsiveManager.init();
  return responsiveManager;
};
